# 内网包解决方案

## 概述

将4个内网包存储在git仓库的 `repo-packages/` 目录中，通过脚本自动复制到 `local-packages/` 供npm使用。

**支持系统**: Windows、Linux、macOS  
**Node.js版本**: 12.1.0+

## 目录结构

```
项目根目录/
├── repo-packages/          # 存储内网包（提交到git）
│   ├── @picc/
│   │   ├── verifition/     # 验证码组件
│   │   └── watermark/      # 水印组件
│   ├── web-plugin/         # web插件包
│   └── img-async-load/     # 图片异步加载
├── local-packages/         # 自动生成（不提交）
├── scripts/
│   └── setup-packages.js  # 复制脚本
└── package.json           # 已配置本地包路径
```

## 使用步骤

### 1. 放置内网包

将你的4个内网包完整复制到 `repo-packages/` 目录：

```bash
# 创建目录结构
mkdir -p repo-packages/@picc/verifition
mkdir -p repo-packages/@picc/watermark  
mkdir -p repo-packages/web-plugin
mkdir -p repo-packages/img-async-load

# 复制真实的包文件
# 将完整的包内容（包括package.json、dist/等）复制到对应目录
```

### 2. 提交到git

```bash
git add repo-packages/
git commit -m "添加内网包"
git push
```

### 3. 安装依赖

```bash
# 会自动运行 setup-packages.js 复制包
npm install
```

## 脚本说明

### `npm run setup:packages`
- 从 `repo-packages/` 复制包到 `local-packages/`
- 支持Windows和Unix系统
- 自动在 `npm install` 时执行

### 手动运行
```bash
# 手动复制包
node scripts/setup-packages.js

# 重新安装依赖
npm install
```

## package.json 配置

已修改的依赖配置：
```json
{
  "dependencies": {
    "@picc/verifition": "file:./local-packages/@picc/verifition",
    "@picc/watermark": "file:./local-packages/@picc/watermark",
    "web-plugin": "file:./local-packages/web-plugin", 
    "img-async-load": "file:./local-packages/img-async-load"
  },
  "scripts": {
    "setup:packages": "node scripts/setup-packages.js",
    "postinstall": "npm run setup:packages"
  }
}
```

## 跨平台支持

脚本自动检测操作系统并使用对应命令：

| 操作 | Windows | Unix/Linux |
|------|---------|------------|
| 创建目录 | `mkdir` | `mkdir -p` |
| 删除目录 | `rmdir /s /q` | `rm -rf` |
| 复制目录 | `xcopy /e /i /h /y` | `cp -r` |

## 版本控制

### 提交到git ✅
- `repo-packages/` - 内网包源文件
- `scripts/setup-packages.js` - 复制脚本
- `package.json` - 依赖配置

### 不提交到git ❌  
- `local-packages/` - 自动生成
- `node_modules/` - npm依赖

## 故障排除

### 问题1：包不存在
```bash
# 检查repo-packages目录
ls repo-packages/
# 或 Windows: dir repo-packages\

# 确保包文件完整
ls repo-packages/@picc/verifition/
```

### 问题2：复制失败
```bash
# 手动运行脚本查看详细错误
node scripts/setup-packages.js
```

### 问题3：权限问题
```bash
# Linux/macOS: 检查权限
chmod -R 755 repo-packages/

# Windows: 以管理员身份运行
```

## 优势

1. **完全离线** - 无需内网连接
2. **跨平台** - 支持Windows/Linux/macOS  
3. **自动化** - npm install自动处理
4. **版本控制** - 内网包受git管理
5. **简单维护** - 只需一个脚本文件
