# Git仓库内网包解决方案

## 方案概述

将4个内网包直接存储在git仓库中，通过脚本自动复制到本地包目录，实现无内网环境的依赖安装。

## 目录结构

```
项目根目录/
├── repo-packages/          # 存储在git中的内网包（需要提交）
│   ├── @picc/
│   │   ├── verifition/     # 验证码组件 v0.0.1
│   │   │   ├── package.json
│   │   │   ├── dist/
│   │   │   └── ...
│   │   └── watermark/      # 水印组件 v1.0.0
│   │       ├── package.json
│   │       ├── dist/
│   │       └── ...
│   ├── web-plugin/         # web插件包 v4.1.0
│   │   ├── package.json
│   │   ├── index.js
│   │   └── ...
│   └── img-async-load/     # 图片异步加载 v1.0.0
│       ├── package.json
│       ├── index.js
│       └── ...
├── local-packages/         # 自动生成的本地包目录（不提交）
├── scripts/                # 自动化脚本
└── package.json           # 已配置使用本地包路径
```

## 设置步骤

### 1. 创建仓库结构

```bash
# 创建推荐的目录结构
npm run create:repo-structure
```

### 2. 放置内网包

将你的4个内网包完整复制到对应目录：

```bash
# 示例：从其他地方复制真实的包文件
cp -r /path/to/real/@picc/verifition/* repo-packages/@picc/verifition/
cp -r /path/to/real/@picc/watermark/* repo-packages/@picc/watermark/
cp -r /path/to/real/web-plugin/* repo-packages/web-plugin/
cp -r /path/to/real/img-async-load/* repo-packages/img-async-load/
```

### 3. 提交到git

```bash
# 添加内网包到git
git add repo-packages/
git commit -m "添加内网包到仓库"
git push
```

### 4. 设置本地包

```bash
# 从repo-packages复制到local-packages
npm run setup:local-packages

# 安装依赖
npm install
```

## 日常使用

### 新环境初始化

```bash
# 1. 克隆项目
git clone [项目地址]
cd pdfc-web-4.1.0.0

# 2. 安装依赖（会自动设置本地包）
npm install
```

### 更新内网包

```bash
# 1. 更新repo-packages中的包文件
# 2. 重新设置本地包
npm run setup:local-packages

# 3. 重新安装
rm -rf node_modules package-lock.json
npm install
```

## 脚本说明

### `npm run create:repo-structure`
- 创建 `repo-packages/` 目录结构
- 生成示例 `package.json` 文件
- 更新 `.gitignore` 配置

### `npm run setup:local-packages`
- 从 `repo-packages/` 复制包到 `local-packages/`
- 自动在 `postinstall` 钩子中执行

### `npm run install:local`
- 一键清理并重新安装所有依赖

## package.json 配置

已修改的依赖配置：
```json
{
  "dependencies": {
    "@picc/verifition": "file:./local-packages/@picc/verifition",
    "@picc/watermark": "file:./local-packages/@picc/watermark",
    "web-plugin": "file:./local-packages/web-plugin",
    "img-async-load": "file:./local-packages/img-async-load"
  }
}
```

## 版本控制策略

### 提交到git
- ✅ `repo-packages/` - 内网包源文件
- ✅ `scripts/` - 自动化脚本
- ✅ `package.json` - 依赖配置
- ✅ `*.md` - 文档文件

### 不提交到git
- ❌ `local-packages/` - 自动生成的本地包
- ❌ `node_modules/` - npm依赖
- ❌ `package-lock.json` - 锁定文件（可选）

## 优势

1. **完全离线**：无需内网连接
2. **版本控制**：内网包版本受git管理
3. **团队协作**：所有人使用相同的包版本
4. **自动化**：脚本处理所有复制操作
5. **兼容性**：支持Node.js 12.1.0

## 注意事项

1. **包大小**：注意git仓库大小，建议只包含必要文件
2. **更新频率**：内网包更新时需要重新提交
3. **权限管理**：确保团队成员有仓库访问权限
4. **备份策略**：定期备份内网包文件

## 故障排除

### 问题1：包不存在
```bash
# 检查repo-packages目录
ls -la repo-packages/

# 重新创建结构
npm run create:repo-structure
```

### 问题2：复制失败
```bash
# 检查权限
ls -la repo-packages/@picc/

# 手动复制
cp -r repo-packages/@picc local-packages/
```

### 问题3：安装失败
```bash
# 清理重装
rm -rf local-packages node_modules package-lock.json
npm run setup:local-packages
npm install
```
