{"name": "pdfc3-web", "version": "4.0.2", "scripts": {"test:unit": "jest --clearCache && vue-cli-service test:unit", "lint": "eslint --ext .js --ext .jsx --ext .vue src/", "build:performance": "vue-cli-service build --mode performance  --report", "build:prod": "vue-cli-service build --modern", "build:stage": "vue-cli-service build --mode staging", "build:internetprod": "vue-cli-service build --mode internet --no-module", "build:intranetprod": "vue-cli-service build --mode intranet --no-module", "dev": "vue-cli-service serve", "lint-fix": "eslint --fix --ext", "new": "plop", "preview": "node build/index.js --preview", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "test:ci": "npm run lint && npm run test:unit", "setup:local-packages": "node scripts/setup-local-packages-node12.js", "install:local": "bash scripts/install-with-local-packages.sh", "create:repo-structure": "node scripts/create-repo-structure.js", "postinstall": "npm run setup:local-packages"}, "dependencies": {"@picc/verifition": "file:./local-packages/@picc/verifition", "@picc/watermark": "file:./local-packages/@picc/watermark", "axios": "^1.4.0", "base64url": "^3.0.1", "core-js": "^3.8.3", "crypto-js": "^4.2.0", "echarts": "^5.3.3", "element-ui": "^2.15.6", "html2canvas": "^1.4.1", "img-async-load": "file:./local-packages/img-async-load", "js-cookie": "2.2.0", "jsrsasign": "^10.5.25", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "qiankun": "^2.6.3", "register-service-worker": "^1.7.2", "uuid": "^8.1.0", "vee-validate": "^2.2.13", "vue": "^2.6.14", "vue-i18n": "7.3.2", "vue-router": "^3.5.3", "vuedraggable": "^2.24.3", "vuex": "^3.6.2", "web-plugin": "file:./local-packages/web-plugin", "whatwg-fetch": "^3.6.2", "xss": "^1.0.15"}, "devDependencies": {"@babel/core": "^7.16.5", "@babel/eslint-parser": "^7.16.5", "@babel/polyfill": "^7.7.0", "@babel/register": "^7.16.5", "@vue/cli-plugin-babel": "^5.0.0-rc.1", "@vue/cli-plugin-eslint": "^5.0.0-rc.1", "@vue/cli-plugin-router": "^5.0.0-rc.1", "@vue/cli-plugin-unit-jest": "^5.0.0-rc.1", "@vue/cli-plugin-vuex": "^5.0.0-rc.1", "@vue/cli-service": "^5.0.0-rc.1", "@vue/test-utils": "1.0.0-beta.29", "@vue/vue2-jest": "^27.0.0-alpha.3", "autoprefixer": "^9.5.1", "babel-core": "7.0.0-bridge.0", "babel-jest": "27.5.1", "babel-plugin-transform-remove-console": "^6.9.4", "babel-plugin-transform-runtime": "^6.23.0", "babel-polyfill": "^6.26.0", "chalk": "2.4.2", "compression-webpack-plugin": "^4.0.0", "connect": "3.6.6", "eslint": "^7.32.0", "eslint-plugin-html": "^6.2.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "gzip-loader": "0.0.1", "happypack": "^5.0.1", "hard-source-webpack-plugin": "^0.13.1", "html-webpack-plugin": "^5.5.0", "husky": "1.3.1", "jest": "^27.1.0", "lint-staged": "8.1.5", "mini-css-extract-plugin": "^0.11.3", "plop": "^3.1.1", "runjs": "^4.3.2", "sass": "^1.51.0", "sass-loader": "^10.2.0", "script-ext-html-webpack-plugin": "^2.1.5", "serve-static": "^1.13.2", "speed-measure-webpack-plugin": "^1.3.3", "svg-inline-loader": "^0.8.2", "svg-sprite-loader": "^6.0.11", "svgo": "1.2.0", "svgo-loader": "^2.2.1", "terser-webpack-plugin": "^5.0.3", "thread-loader": "^3.0.1", "vue-highlightjs": "^1.3.3", "vue-quill-editor": "^3.0.6", "vue-template-compiler": "^2.6.14", "webpack-merge": "^5.4.0"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "path": "dist/car", "volta": {"node": "12.1.0"}}