# 手动设置本地内网包指南

## 当前情况
- Node.js 版本: 12.1.0
- 需要处理的内网包: `@picc/verifition`, `@picc/watermark`, `web-plugin`, `img-async-load`

## 方案一：手动复制包（推荐）

### 步骤1：获取内网包
如果你有其他能连接内网的机器，在那台机器上：

```bash
# 1. 克隆项目并安装依赖
git clone [项目地址]
cd pdfc-web-4.1.0.0
npm config set registry http://10.10.1.68:8082/repository/npm-all/
npm install

# 2. 打包内网包
mkdir internal-packages-backup
cp -r node_modules/@picc internal-packages-backup/
cp -r node_modules/web-plugin internal-packages-backup/
cp -r node_modules/img-async-load internal-packages-backup/

# 3. 压缩传输
tar -czf internal-packages.tar.gz internal-packages-backup/
```

### 步骤2：在当前机器上设置

```bash
# 1. 解压内网包
tar -xzf internal-packages.tar.gz

# 2. 创建本地包目录
mkdir -p local-packages

# 3. 复制包
cp -r internal-packages-backup/@picc local-packages/
cp -r internal-packages-backup/web-plugin local-packages/
cp -r internal-packages-backup/img-async-load local-packages/

# 4. 清理
rm -rf internal-packages-backup internal-packages.tar.gz
```

### 步骤3：验证目录结构

确保目录结构如下：
```
local-packages/
├── @picc/
│   ├── verifition/
│   │   ├── package.json
│   │   └── dist/
│   └── watermark/
│       ├── package.json
│       └── dist/
├── web-plugin/
│   ├── package.json
│   └── ...
└── img-async-load/
    ├── package.json
    └── ...
```

### 步骤4：安装依赖

```bash
# 清理现有依赖
rm -rf node_modules package-lock.json

# 重新安装（会使用本地包）
npm install
```

## 方案二：使用脚本（如果有完整的 node_modules）

如果当前 node_modules 中已经有完整的内网包：

```bash
# 运行设置脚本
node scripts/setup-local-packages-node12.js

# 重新安装
rm -rf node_modules package-lock.json
npm install
```

## 验证安装

安装完成后，检查内网包是否正确链接：

```bash
# 检查包是否存在
ls -la node_modules/@picc/
ls -la node_modules/web-plugin/
ls -la node_modules/img-async-load/

# 检查是否是符号链接或本地文件
file node_modules/@picc/verifition
file node_modules/web-plugin
```

## 故障排除

### 问题1：包不存在
```bash
# 检查本地包目录
ls -la local-packages/

# 检查 package.json 配置
grep -A 10 -B 2 "file:" package.json
```

### 问题2：安装失败
```bash
# 查看详细错误
npm install --verbose

# 检查 npm 配置
npm config list
```

### 问题3：版本不匹配
检查每个包的 package.json：
```bash
cat local-packages/@picc/verifition/package.json | grep version
cat local-packages/@picc/watermark/package.json | grep version
cat local-packages/web-plugin/package.json | grep version
cat local-packages/img-async-load/package.json | grep version
```

## 团队协作

1. **分享本地包**：
   ```bash
   # 打包本地包目录
   tar -czf local-packages.tar.gz local-packages/
   
   # 其他人解压使用
   tar -xzf local-packages.tar.gz
   npm install
   ```

2. **版本控制**：
   - 不要提交 `local-packages/` 到 git
   - 可以提交 `local-packages.tar.gz` 到内部文件服务器

## 注意事项

1. **Node.js 12 兼容性**：当前脚本已针对 Node.js 12.1.0 优化
2. **路径问题**：确保所有路径使用相对路径
3. **权限问题**：确保有读写权限
4. **网络问题**：本方案不需要网络连接
