# 本地内网包解决方案

## 问题描述
项目依赖4个内网包，在无法连接内网的环境下无法正常安装依赖：
- `@picc/verifition` - 验证码组件
- `@picc/watermark` - 水印组件  
- `web-plugin` - web插件包
- `img-async-load` - 图片异步加载组件

## 解决方案

### 方案一：手动设置（推荐）

1. **首次设置**（在有内网环境时执行）：
```bash
# 1. 正常安装依赖（需要内网环境）
npm install

# 2. 设置本地包
node scripts/setup-local-packages.js

# 3. 重新安装使用本地包
rm -rf node_modules package-lock.json
npm install
```

2. **后续使用**（无内网环境）：
```bash
# 直接安装，会自动使用本地包
npm install
```

### 方案二：使用脚本自动化

```bash
# 一键安装（使用本地包）
npm run install:local
```

## 目录结构

```
project/
├── local-packages/          # 本地内网包存储目录
│   ├── @picc/
│   │   ├── verifition/
│   │   └── watermark/
│   ├── web-plugin/
│   └── img-async-load/
├── scripts/
│   ├── setup-local-packages.js      # 设置本地包脚本
│   └── install-with-local-packages.sh # 安装脚本
└── package.json             # 已修改为使用本地包路径
```

## package.json 修改说明

原来的依赖配置：
```json
{
  "dependencies": {
    "@picc/verifition": "0.0.1",
    "@picc/watermark": "^1.0.0",
    "web-plugin": "^4.1.0",
    "img-async-load": "^1.0.0"
  }
}
```

修改后的配置：
```json
{
  "dependencies": {
    "@picc/verifition": "file:./local-packages/@picc/verifition",
    "@picc/watermark": "file:./local-packages/@picc/watermark", 
    "web-plugin": "file:./local-packages/web-plugin",
    "img-async-load": "file:./local-packages/img-async-load"
  }
}
```

## 注意事项

1. **版本控制**：
   - `local-packages/` 目录已添加到 `.gitignore`
   - 不要将内网包提交到代码仓库

2. **包更新**：
   - 如需更新内网包，需要在有内网环境时重新执行设置脚本
   - 或手动替换 `local-packages/` 目录中的包

3. **团队协作**：
   - 团队成员需要共享 `local-packages/` 目录内容
   - 可通过其他方式（如网盘、内部文件服务器）分发

## 故障排除

### 问题1：包不存在
```bash
# 检查本地包目录
ls -la local-packages/

# 重新设置
node scripts/setup-local-packages.js
```

### 问题2：安装失败
```bash
# 清理后重新安装
rm -rf node_modules package-lock.json
npm install
```

### 问题3：包版本不匹配
```bash
# 检查包版本
cat local-packages/@picc/verifition/package.json | grep version
cat local-packages/@picc/watermark/package.json | grep version
```

## 其他方案对比

| 方案 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| 本地文件路径 | 简单直接，npm原生支持 | 需要手动管理包文件 | 小团队，包更新不频繁 |
| npm link | 开发友好，支持实时更新 | 需要全局安装包 | 开发环境 |
| 私有npm仓库 | 专业，支持版本管理 | 需要搭建维护服务器 | 大团队，企业级 |
| patch-package | 可以修改包内容 | 主要用于补丁，不适合完整包 | 需要修改第三方包 |
