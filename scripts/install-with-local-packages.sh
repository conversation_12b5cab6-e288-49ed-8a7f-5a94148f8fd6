#!/bin/bash

echo "🚀 开始安装依赖（使用本地内网包）..."

# 检查本地包目录是否存在
if [ ! -d "./local-packages" ]; then
    echo "❌ 本地包目录不存在，请先运行 setup-local-packages.js"
    echo "   node scripts/setup-local-packages.js"
    exit 1
fi

# 清理现有依赖
echo "🧹 清理现有依赖..."
rm -rf node_modules package-lock.json

# 安装依赖
echo "📦 安装依赖..."
npm install

# 验证内网包是否正确安装
echo "🔍 验证内网包安装..."
PACKAGES=("@picc/verifition" "@picc/watermark" "web-plugin" "img-async-load")

for package in "${PACKAGES[@]}"; do
    if [ -d "node_modules/$package" ]; then
        echo "✅ $package - 已安装"
    else
        echo "❌ $package - 未安装"
    fi
done

echo "🎉 依赖安装完成！"
