#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 环境检查...\n');

// 检查 Node.js 版本
console.log('Node.js 版本:', process.version);

// 检查内网包是否存在
const INTERNAL_PACKAGES = [
  '@picc/verifition',
  '@picc/watermark',
  'web-plugin',
  'img-async-load'
];

console.log('\n📦 检查仓库包 (repo-packages):');
INTERNAL_PACKAGES.forEach(packageName => {
  const packagePath = path.join('./repo-packages', packageName);
  const exists = fs.existsSync(packagePath);
  console.log(`${exists ? '✅' : '❌'} ${packageName}: ${exists ? '存在' : '不存在'}`);

  if (exists) {
    const packageJsonPath = path.join(packagePath, 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      try {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        console.log(`    版本: ${packageJson.version || '未知'}`);
      } catch (error) {
        console.log(`    ⚠️  package.json 解析失败`);
      }
    } else {
      console.log(`    ❌ 缺少 package.json`);
    }
  }
});

console.log('\n📦 检查node_modules中的内网包:');
INTERNAL_PACKAGES.forEach(packageName => {
  const packagePath = path.join('./node_modules', packageName);
  const exists = fs.existsSync(packagePath);
  console.log(`${exists ? '✅' : '❌'} ${packageName}: ${exists ? '存在' : '不存在'}`);
});

// 检查本地包目录
console.log('\n📁 检查本地包目录:');
const localPackagesDir = './local-packages';
if (fs.existsSync(localPackagesDir)) {
  console.log('✅ local-packages 目录存在');

  INTERNAL_PACKAGES.forEach(packageName => {
    const localPackagePath = path.join(localPackagesDir, packageName);
    const exists = fs.existsSync(localPackagePath);
    console.log(`  ${exists ? '✅' : '❌'} ${packageName}: ${exists ? '存在' : '不存在'}`);
  });
} else {
  console.log('❌ local-packages 目录不存在');
}

console.log('\n📋 建议操作:');
const repoPackagesExists = fs.existsSync('./repo-packages');
const localPackagesExists = fs.existsSync(localPackagesDir);

if (!repoPackagesExists) {
  console.log('1. 首先创建仓库结构: npm run create:repo-structure');
  console.log('2. 将真实的内网包复制到 repo-packages/ 目录');
  console.log('3. 提交到git: git add repo-packages/ && git commit -m "添加内网包"');
} else if (!localPackagesExists) {
  console.log('1. 设置本地包: npm run setup:local-packages');
  console.log('2. 安装依赖: npm install');
} else {
  console.log('✅ 环境配置完整，可以正常使用');
  console.log('如需重新安装: npm run install:local');
}
