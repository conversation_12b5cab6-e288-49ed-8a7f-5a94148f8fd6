#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 环境检查...\n');

// 检查 Node.js 版本
console.log('Node.js 版本:', process.version);

// 检查内网包是否存在
const INTERNAL_PACKAGES = [
  '@picc/verifition',
  '@picc/watermark', 
  'web-plugin',
  'img-async-load'
];

console.log('\n📦 检查内网包:');
INTERNAL_PACKAGES.forEach(packageName => {
  const packagePath = path.join('./node_modules', packageName);
  const exists = fs.existsSync(packagePath);
  console.log(`${exists ? '✅' : '❌'} ${packageName}: ${exists ? '存在' : '不存在'}`);
});

// 检查本地包目录
console.log('\n📁 检查本地包目录:');
const localPackagesDir = './local-packages';
if (fs.existsSync(localPackagesDir)) {
  console.log('✅ local-packages 目录存在');
  
  INTERNAL_PACKAGES.forEach(packageName => {
    const localPackagePath = path.join(localPackagesDir, packageName);
    const exists = fs.existsSync(localPackagePath);
    console.log(`  ${exists ? '✅' : '❌'} ${packageName}: ${exists ? '存在' : '不存在'}`);
  });
} else {
  console.log('❌ local-packages 目录不存在');
}

console.log('\n📋 建议操作:');
if (!fs.existsSync(localPackagesDir)) {
  console.log('1. 运行: node scripts/setup-local-packages-node12.js');
} else {
  console.log('1. 本地包已设置，可以运行: npm install');
}
