#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 内网包列表
const INTERNAL_PACKAGES = [
  '@picc/verifition',
  '@picc/watermark',
  'web-plugin',
  'img-async-load'
];

const LOCAL_PACKAGES_DIR = './local-packages';
const REPO_PACKAGES_DIR = './repo-packages'; // git仓库中的包目录

console.log('🚀 开始设置本地内网包（Node.js 12 兼容版本）...\n');

try {
  // 1. 创建本地包目录
  console.log('📁 创建本地包目录...');
  execSync(`mkdir -p "${LOCAL_PACKAGES_DIR}"`, { stdio: 'inherit' });
  console.log('✅ 创建本地包目录:', LOCAL_PACKAGES_DIR);

  // 2. 复制内网包（从git仓库中的预置目录）
  console.log('\n📦 复制内网包...');
  INTERNAL_PACKAGES.forEach(packageName => {
    const sourcePath = path.join(REPO_PACKAGES_DIR, packageName);
    const targetPath = path.join(LOCAL_PACKAGES_DIR, packageName);

    console.log(`处理包: ${packageName}`);

    if (fs.existsSync(sourcePath)) {
      try {
        // 删除目标目录（如果存在）
        execSync(`rm -rf "${targetPath}"`, { stdio: 'pipe' });

        // 创建目标目录的父目录
        const targetDir = path.dirname(targetPath);
        execSync(`mkdir -p "${targetDir}"`, { stdio: 'pipe' });

        // 复制包
        execSync(`cp -r "${sourcePath}" "${targetPath}"`, { stdio: 'inherit' });
        console.log(`✅ 复制成功: ${packageName}`);
      } catch (error) {
        console.error(`❌ 复制失败: ${packageName}`, error.message);
      }
    } else {
      console.warn(`⚠️  包不存在: ${packageName} (${sourcePath})`);
    }
  });

  // 3. 更新 .gitignore
  console.log('\n📝 更新 .gitignore...');
  const gitignorePath = './.gitignore';
  const gitignoreEntry = '\n# 本地内网包\nlocal-packages/\n';

  if (fs.existsSync(gitignorePath)) {
    const gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
    if (!gitignoreContent.includes('local-packages/')) {
      fs.appendFileSync(gitignorePath, gitignoreEntry);
      console.log('✅ 更新 .gitignore');
    } else {
      console.log('ℹ️  .gitignore 已包含 local-packages/');
    }
  } else {
    fs.writeFileSync(gitignorePath, gitignoreEntry);
    console.log('✅ 创建 .gitignore');
  }

  console.log('\n🎉 本地内网包设置完成！');
  console.log('\n📝 下一步操作：');
  console.log('1. 删除 node_modules 和 package-lock.json');
  console.log('2. 运行 npm install 重新安装依赖');
  console.log('\n命令：');
  console.log('rm -rf node_modules package-lock.json');
  console.log('npm install');

} catch (error) {
  console.error('❌ 设置失败:', error.message);
  process.exit(1);
}
