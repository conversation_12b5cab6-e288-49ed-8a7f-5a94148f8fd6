#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Node.js 12 兼容性函数
function removeSync(targetPath) {
  if (fs.existsSync(targetPath)) {
    try {
      // 使用 rm -rf 命令删除目录（兼容 Node.js 12）
      execSync(`rm -rf "${targetPath}"`, { stdio: 'pipe' });
    } catch (error) {
      console.warn(`警告: 删除目录失败 ${targetPath}:`, error.message);
    }
  }
}

function mkdirSync(dirPath) {
  try {
    // 使用 mkdir -p 命令创建目录（兼容 Node.js 12）
    execSync(`mkdir -p "${dirPath}"`, { stdio: 'pipe' });
  } catch (error) {
    console.warn(`警告: 创建目录失败 ${dirPath}:`, error.message);
  }
}

// 内网包列表
const INTERNAL_PACKAGES = [
  '@picc/verifition',
  '@picc/watermark',
  'web-plugin',
  'img-async-load'
];

const LOCAL_PACKAGES_DIR = './local-packages';
const REPO_PACKAGES_DIR = './repo-packages'; // git仓库中的包目录

console.log('🚀 开始设置本地内网包...\n');

// 1. 创建本地包目录
if (!fs.existsSync(LOCAL_PACKAGES_DIR)) {
  mkdirSync(LOCAL_PACKAGES_DIR);
  console.log('✅ 创建本地包目录:', LOCAL_PACKAGES_DIR);
}

// 2. 复制内网包（从git仓库中的预置目录）
INTERNAL_PACKAGES.forEach(packageName => {
  const sourcePath = path.join(REPO_PACKAGES_DIR, packageName);
  const targetPath = path.join(LOCAL_PACKAGES_DIR, packageName);

  if (fs.existsSync(sourcePath)) {
    // 如果目标目录存在，先删除（使用兼容函数）
    removeSync(targetPath);

    // 创建目标目录的父目录
    const targetDir = path.dirname(targetPath);
    if (!fs.existsSync(targetDir)) {
      mkdirSync(targetDir);
    }

    // 复制包
    try {
      execSync(`cp -r "${sourcePath}" "${targetPath}"`, { stdio: 'inherit' });
      console.log(`✅ 复制包: ${packageName}`);
    } catch (error) {
      console.error(`❌ 复制包失败: ${packageName}`, error.message);
    }
  } else {
    console.warn(`⚠️  包不存在: ${packageName} (${sourcePath})`);
  }
});

// 3. 创建 .gitignore 条目（如果需要）
const gitignorePath = './.gitignore';
const gitignoreEntry = '\n# 本地内网包\nlocal-packages/\n';

if (fs.existsSync(gitignorePath)) {
  const gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
  if (!gitignoreContent.includes('local-packages/')) {
    fs.appendFileSync(gitignorePath, gitignoreEntry);
    console.log('✅ 更新 .gitignore');
  }
} else {
  fs.writeFileSync(gitignorePath, gitignoreEntry);
  console.log('✅ 创建 .gitignore');
}

console.log('\n🎉 本地内网包设置完成！');
console.log('\n📝 下一步操作：');
console.log('1. 删除 node_modules 和 package-lock.json');
console.log('2. 运行 npm install 重新安装依赖');
console.log('\n命令：');
console.log('rm -rf node_modules package-lock.json');
console.log('npm install');
