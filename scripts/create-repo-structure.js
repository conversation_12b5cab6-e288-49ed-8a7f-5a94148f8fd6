#!/usr/bin/env node

const fs = require('fs');
const { execSync } = require('child_process');

console.log('🏗️  创建推荐的仓库结构...\n');

// 创建 repo-packages 目录结构
const directories = [
  'repo-packages/@picc/verifition',
  'repo-packages/@picc/watermark',
  'repo-packages/web-plugin',
  'repo-packages/img-async-load'
];

directories.forEach(dir => {
  try {
    execSync(`mkdir -p "${dir}"`, { stdio: 'pipe' });
    console.log(`✅ 创建目录: ${dir}`);
  } catch (error) {
    console.error(`❌ 创建目录失败: ${dir}`, error.message);
  }
});

// 创建示例 package.json 文件
const packageTemplates = {
  '@picc/verifition': {
    name: '@picc/verifition',
    version: '0.0.1',
    description: 'PICC verification component',
    main: 'dist/index.js',
    files: ['dist']
  },
  '@picc/watermark': {
    name: '@picc/watermark',
    version: '1.0.0',
    description: 'PICC watermark component',
    main: 'dist/index.js',
    files: ['dist']
  },
  'web-plugin': {
    name: 'web-plugin',
    version: '4.1.0',
    description: 'PICC web plugin',
    main: 'index.js'
  },
  'img-async-load': {
    name: 'img-async-load',
    version: '1.0.0',
    description: 'Image async load component',
    main: 'index.js'
  }
};

console.log('\n📝 创建示例 package.json 文件...');
Object.entries(packageTemplates).forEach(([packageName, packageJson]) => {
  const packagePath = `repo-packages/${packageName}/package.json`;
  
  if (!fs.existsSync(packagePath)) {
    try {
      fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
      console.log(`✅ 创建: ${packagePath}`);
    } catch (error) {
      console.error(`❌ 创建失败: ${packagePath}`, error.message);
    }
  } else {
    console.log(`ℹ️  已存在: ${packagePath}`);
  }
});

// 创建 .gitignore 条目
console.log('\n📋 更新 .gitignore...');
const gitignorePath = './.gitignore';
const gitignoreEntries = [
  '# 本地包目录（自动生成）',
  'local-packages/',
  '',
  '# 保留仓库包目录（需要提交）',
  '# repo-packages/ - 不要忽略这个目录',
  ''
].join('\n');

try {
  if (fs.existsSync(gitignorePath)) {
    const gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
    if (!gitignoreContent.includes('local-packages/')) {
      fs.appendFileSync(gitignorePath, '\n' + gitignoreEntries);
      console.log('✅ 更新 .gitignore');
    } else {
      console.log('ℹ️  .gitignore 已包含相关配置');
    }
  } else {
    fs.writeFileSync(gitignorePath, gitignoreEntries);
    console.log('✅ 创建 .gitignore');
  }
} catch (error) {
  console.error('❌ 更新 .gitignore 失败:', error.message);
}

console.log('\n🎉 仓库结构创建完成！');
console.log('\n📁 推荐的目录结构：');
console.log('repo-packages/           # 提交到git的内网包目录');
console.log('├── @picc/');
console.log('│   ├── verifition/      # 验证码组件');
console.log('│   └── watermark/       # 水印组件');
console.log('├── web-plugin/          # web插件包');
console.log('└── img-async-load/      # 图片异步加载组件');
console.log('');
console.log('local-packages/          # 自动生成的本地包目录（不提交）');
console.log('');
console.log('📝 下一步操作：');
console.log('1. 将真实的内网包文件复制到 repo-packages/ 对应目录');
console.log('2. 运行: npm run setup:local-packages');
console.log('3. 运行: npm install');
